/**
 * 统一用户角色和权限管理
 * 整合各文件中分散的用户角色映射和权限常量，提供统一的角色权限管理
 */

// 用户类型映射 - 映射数字类型到字符串
export const USER_TYPES = {
  1: 'admin',     // 超管
  2: 'manager',   // 管理
  3: 'employee',  // 员工
  'ip_user': 'ip_user',  // IP用户
  'user': 'user',        // 普通用户
  'wechat_user': 'wechat_user'  // 微信用户
}

// 用户类型显示名称
export const USER_TYPE_NAMES = {
  1: '超管',
  2: '管理',
  3: '员工',
  admin: '超管',
  manager: '管理',
  employee: '员工',
  user: '用户',
  ip_user: 'IP用户',
  wechat_user: '微信用户',
  guest: '游客'
}

// 用户角色定义
export const USER_ROLES = {
  SUPER_ADMIN: 'super_admin',
  ADMIN: 'admin',
  MANAGER: 'manager',
  AGENT: 'agent', // 管理的别名
  EMPLOYEE: 'employee',
  USER: 'user',
  WECHAT_USER: 'wechat_user',
  IP_USER: 'ip_user',
  GUEST: 'guest'
}

// 权限级别定义（数字越大权限越高）
export const PERMISSION_LEVELS = {
  guest: 0,
  user: 1,
  wechat_user: 1,
  ip_user: 1,
  employee: 2,
  manager: 3,
  agent: 3,
  admin: 4,
  super_admin: 5
}

// 权限类型
export const PERMISSIONS = {
  // 基础权限
  VIEW_VIDEOS: 'view_videos',
  TAKE_QUIZ: 'take_quiz',
  VIEW_REWARDS: 'view_rewards',

  // 管理权限
  VIEW_DASHBOARD: 'view_dashboard',
  MANAGE_USERS: 'manage_users',
  MANAGE_VIDEOS: 'manage_videos',
  MANAGE_QUIZ: 'manage_quiz',
  VIEW_REPORTS: 'view_reports',
  MANAGE_EMPLOYEES: 'manage_employees',

  // 高级权限
  SYSTEM_CONFIG: 'system_config',
  USER_MANAGEMENT: 'user_management',
  DATA_EXPORT: 'data_export',

  // 超级管理员权限
  ALL: '*'
}

// 角色权限映射
export const ROLE_PERMISSIONS = {
  guest: [],
  user: [PERMISSIONS.VIEW_VIDEOS, PERMISSIONS.TAKE_QUIZ, PERMISSIONS.VIEW_REWARDS],
  wechat_user: [PERMISSIONS.VIEW_VIDEOS, PERMISSIONS.TAKE_QUIZ, PERMISSIONS.VIEW_REWARDS],
  ip_user: [PERMISSIONS.VIEW_VIDEOS, PERMISSIONS.TAKE_QUIZ, PERMISSIONS.VIEW_REWARDS],
  employee: [
    PERMISSIONS.VIEW_VIDEOS, 
    PERMISSIONS.TAKE_QUIZ, 
    PERMISSIONS.VIEW_REWARDS, 
    PERMISSIONS.VIEW_DASHBOARD
  ],
  manager: [
    PERMISSIONS.VIEW_DASHBOARD, 
    PERMISSIONS.MANAGE_USERS, 
    PERMISSIONS.VIEW_REPORTS, 
    PERMISSIONS.VIEW_VIDEOS, 
    PERMISSIONS.TAKE_QUIZ, 
    PERMISSIONS.MANAGE_EMPLOYEES
  ],
  agent: [
    PERMISSIONS.VIEW_DASHBOARD, 
    PERMISSIONS.MANAGE_EMPLOYEES, 
    PERMISSIONS.VIEW_REPORTS, 
    PERMISSIONS.MANAGE_USERS, 
    PERMISSIONS.VIEW_VIDEOS, 
    PERMISSIONS.TAKE_QUIZ
  ],
  admin: [PERMISSIONS.ALL],
  super_admin: [PERMISSIONS.ALL]
}

// 页面访问权限配置
export const PAGE_PERMISSIONS = {
  // 管理后台页面
  '/pages/index/index': ['employee', 'manager', 'agent', 'admin', 'super_admin'],
  '/pages/admin/users/user-management': ['employee', 'manager', 'agent', 'admin', 'super_admin'], // 员工可以管理用户
  '/pages/admin/users/employee-list': ['manager', 'agent', 'admin', 'super_admin'],
  '/pages/admin/users/manager-list': ['admin', 'super_admin'],
  '/pages/admin/media/index': ['employee', 'manager', 'agent', 'admin', 'super_admin'], // 员工可以查看批次信息
  '/pages/admin/media/upload': ['manager', 'agent', 'admin', 'super_admin'],
  '/pages/admin/media/publish': ['manager', 'agent', 'admin', 'super_admin'],

  // 用户页面
  '/pages/video/index': ['user', 'wechat_user', 'manager', 'agent', 'admin', 'super_admin'], // 员工不能看视频页面
  '/pages/user/index': ['user', 'wechat_user', 'employee', 'manager', 'agent', 'admin', 'super_admin']
}

// 功能权限配置
export const FEATURE_PERMISSIONS = {
  // 基础功能权限
  'view_dashboard': ['employee', 'manager', 'agent', 'admin', 'super_admin'],
  'manage_users': ['employee'], // 只有员工可以管理用户
  'user_audit': ['employee'], // 只有员工可以审核用户

  // 用户管理功能
  'create_user': ['employee'], // 只有员工可以创建用户
  'edit_user': ['employee'], // 只有员工可以编辑用户
  'delete_user': ['admin', 'super_admin'], // 只有管理员以上可以删除用户
  'disable_user': ['employee'], // 只有员工可以禁用用户

  // 员工管理功能
  'create_employee': ['manager', 'agent', 'admin', 'super_admin'],
  'edit_employee': ['manager', 'agent', 'admin', 'super_admin'],
  'delete_employee': ['admin', 'super_admin'],

  // 管理员管理功能
  'create_manager': ['admin', 'super_admin'],
  'edit_manager': ['admin', 'super_admin'],
  'delete_manager': ['super_admin'],

  // 视频管理功能
  'manage_videos': ['manager', 'agent', 'admin', 'super_admin'], // 员工不能管理视频
  'upload_video': ['manager', 'agent', 'admin', 'super_admin'],
  'edit_video': ['manager', 'agent', 'admin', 'super_admin'],
  'delete_video': ['admin', 'super_admin'],
  'publish_video': ['manager', 'agent', 'admin', 'super_admin'],

  // 批次管理功能
  'view_batch': ['employee', 'manager', 'agent', 'admin', 'super_admin'], // 员工可以查看批次
  'create_batch': ['manager', 'agent', 'admin', 'super_admin'], // 员工不能创建批次
  'manage_batch': ['employee', 'manager', 'agent', 'admin', 'super_admin'], // 员工可以管理批次（生成链接等）
  'delete_batch': ['manager', 'agent', 'admin', 'super_admin'], // 管理员以上可以删除批次

  // 数据导出功能
  'export_data': ['admin', 'super_admin'],
  'view_statistics': ['employee', 'manager', 'agent', 'admin', 'super_admin'],

  // 系统配置功能
  'system_config': ['admin', 'super_admin'],
  'user_audit': ['manager', 'agent', 'admin', 'super_admin']
}

/**
 * 角色权限管理器
 */
export class RolePermissionManager {
  /**
   * 获取用户类型显示名称
   * @param {string|number} userType - 用户类型
   * @returns {string} 显示名称
   */
  static getUserTypeName(userType) {
    return USER_TYPE_NAMES[userType] || '未知'
  }

  /**
   * 获取用户角色字符串
   * @param {number} userTypeCode - 用户类型代码
   * @returns {string} 用户角色
   */
  static getUserRole(userTypeCode) {
    return USER_TYPES[userTypeCode] || 'user'
  }

  /**
   * 获取角色权限列表
   * @param {string} role - 用户角色
   * @returns {Array} 权限列表
   */
  static getRolePermissions(role) {
    return ROLE_PERMISSIONS[role] || []
  }

  /**
   * 检查角色是否有指定权限
   * @param {string} role - 用户角色
   * @param {string} permission - 权限名称
   * @returns {boolean} 是否有权限
   */
  static hasPermission(role, permission) {
    const permissions = this.getRolePermissions(role)
    return permissions.includes(PERMISSIONS.ALL) || permissions.includes(permission)
  }

  /**
   * 检查角色是否可以访问指定页面
   * @param {string} role - 用户角色
   * @param {string} pagePath - 页面路径
   * @returns {boolean} 是否可以访问
   */
  static canAccessPage(role, pagePath) {
    const allowedRoles = PAGE_PERMISSIONS[pagePath]
    return allowedRoles ? allowedRoles.includes(role) : true
  }

  /**
   * 检查角色是否可以使用指定功能
   * @param {string} role - 用户角色
   * @param {string} feature - 功能名称
   * @returns {boolean} 是否可以使用
   */
  static canUseFeature(role, feature) {
    const allowedRoles = FEATURE_PERMISSIONS[feature]
    return allowedRoles ? allowedRoles.includes(role) : false
  }

  /**
   * 比较两个角色的权限级别
   * @param {string} role1 - 角色1
   * @param {string} role2 - 角色2
   * @returns {number} 比较结果：1表示role1权限更高，-1表示role2权限更高，0表示相等
   */
  static compareRoles(role1, role2) {
    const level1 = PERMISSION_LEVELS[role1] || 0
    const level2 = PERMISSION_LEVELS[role2] || 0
    
    if (level1 > level2) return 1
    if (level1 < level2) return -1
    return 0
  }

  /**
   * 检查角色是否为管理员角色
   * @param {string} role - 用户角色
   * @returns {boolean} 是否为管理员
   */
  static isAdminRole(role) {
    return ['admin', 'super_admin'].includes(role)
  }

  /**
   * 检查角色是否为管理层角色（包括管理员和经理）
   * @param {string} role - 用户角色
   * @returns {boolean} 是否为管理层
   */
  static isManagementRole(role) {
    return ['admin', 'super_admin', 'manager', 'agent'].includes(role)
  }

  /**
   * 检查角色是否为员工角色
   * @param {string} role - 用户角色
   * @returns {boolean} 是否为员工
   */
  static isEmployeeRole(role) {
    return role === 'employee'
  }

  /**
   * 检查角色是否为普通用户角色
   * @param {string} role - 用户角色
   * @returns {boolean} 是否为普通用户
   */
  static isUserRole(role) {
    return ['user', 'wechat_user', 'ip_user'].includes(role)
  }

  /**
   * 获取角色的所有可访问页面
   * @param {string} role - 用户角色
   * @returns {Array} 可访问的页面路径列表
   */
  static getAccessiblePages(role) {
    return Object.keys(PAGE_PERMISSIONS).filter(page => 
      this.canAccessPage(role, page)
    )
  }

  /**
   * 获取角色的所有可用功能
   * @param {string} role - 用户角色
   * @returns {Array} 可用的功能列表
   */
  static getAvailableFeatures(role) {
    return Object.keys(FEATURE_PERMISSIONS).filter(feature => 
      this.canUseFeature(role, feature)
    )
  }
}

// 便捷导出函数（保持向后兼容）
export const getUserTypeName = RolePermissionManager.getUserTypeName
export const getUserRole = RolePermissionManager.getUserRole
export const getRolePermissions = RolePermissionManager.getRolePermissions
export const hasPermission = RolePermissionManager.hasPermission
export const canAccessPage = RolePermissionManager.canAccessPage
export const canUseFeature = RolePermissionManager.canUseFeature
export const compareRoles = RolePermissionManager.compareRoles
export const isAdminRole = RolePermissionManager.isAdminRole
export const isManagementRole = RolePermissionManager.isManagementRole
export const isEmployeeRole = RolePermissionManager.isEmployeeRole
export const isUserRole = RolePermissionManager.isUserRole

// 默认导出
export default {
  RolePermissionManager,
  USER_TYPES,
  USER_TYPE_NAMES,
  USER_ROLES,
  PERMISSION_LEVELS,
  PERMISSIONS,
  ROLE_PERMISSIONS,
  PAGE_PERMISSIONS,
  FEATURE_PERMISSIONS,
  
  // 便捷函数
  getUserTypeName,
  getUserRole,
  getRolePermissions,
  hasPermission,
  canAccessPage,
  canUseFeature,
  compareRoles,
  isAdminRole,
  isManagementRole,
  isEmployeeRole,
  isUserRole
}
